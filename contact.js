const APP_ID = "8f61c340d00d408997dfa023554e1925";
const TOKEN = "007eJxTYAj1UXW5lhe4uu74hP45e05JnOAt592YF6XY9Sziy9bKq/YKDBZpZobJxiYGKQYGKSYGFpaW5ilpiQZGxqamJqmGlkamnaY9GQ2BjAwh5e1MjAwQCOKzMJTn56QxMAAAJhcehA==";
const CHANNEL = "wolf";
const client = AgoraRTC.createClient({ mode: 'rtc', codec: 'vp8' });

let localTracks = [];
let remoteUsers = {};
let currentCameraId = null;  // Track current camera deviceId
let UID = null;

let joinAndDisplayLocalStream = async () => {
    client.on('user-published', handleUserJoined);
    client.on('user-left', handleUserLeft);

    UID = await client.join(APP_ID, CHANNEL, TOKEN, null);

    localTracks = await AgoraRTC.createMicrophoneAndCameraTracks();

    let player = `<div class="video-container" id="user-container-${UID}">
                        <div class="video-player" id="user-${UID}"></div>
                  </div>`;
    document.getElementById('video-streams').insertAdjacentHTML('beforeend', player);

    localTracks[1].play(`user-${UID}`);

    await client.publish([localTracks[0], localTracks[1]]);
};

let joinStream = async () => {
    await joinAndDisplayLocalStream();
    document.getElementById('join-btn').style.display = 'none';
    document.getElementById('stream-controls').style.display = 'flex';
};

let handleUserJoined = async (user, mediaType) => {
    remoteUsers[user.uid] = user;
    await client.subscribe(user, mediaType);

    if (mediaType === 'video') {
        let player = document.getElementById(`user-container-${user.uid}`);
        if (player != null) {
            player.remove();
        }

        player = `<div class="video-container" id="user-container-${user.uid}">
                        <div class="video-player" id="user-${user.uid}"></div> 
                 </div>`;
        document.getElementById('video-streams').insertAdjacentHTML('beforeend', player);

        user.videoTrack.play(`user-${user.uid}`);
    }

    if (mediaType === 'audio') {
        user.audioTrack.play();
    }
};

let handleUserLeft = async (user) => {
    delete remoteUsers[user.uid];
    document.getElementById(`user-container-${user.uid}`).remove();
};

let leaveAndRemoveLocalStream = async () => {
    for (let i = 0; localTracks.length > i; i++) {
        localTracks[i].stop();
        localTracks[i].close();
    }

    await client.leave();
    document.getElementById('join-btn').style.display = 'block';
    document.getElementById('stream-controls').style.display = 'none';
    document.getElementById('video-streams').innerHTML = '';
};

let toggleMic = async (e) => {
    if (localTracks[0].muted) {
        await localTracks[0].setMuted(false);
        e.target.innerText = '🎙️ Mic On';
        e.target.style.backgroundColor = 'cadetblue';
    } else {
        await localTracks[0].setMuted(true);
        e.target.innerText = '🎙️ Mic Off';
        e.target.style.backgroundColor = '#EE4B2B';
    }
};

let toggleCamera = async (e) => {
    if (localTracks[1].muted) {
        await localTracks[1].setMuted(false);
        e.target.innerText = '📷 Camera On';
        e.target.style.backgroundColor = 'cadetblue';
    } else {
        await localTracks[1].setMuted(true);
        e.target.innerText = '📷 Camera Off';
        e.target.style.backgroundColor = '#EE4B2B';
    }
};

// Switch between front and back cameras
let switchCamera = async () => {
    // Get all video devices (cameras)
    const devices = await AgoraRTC.getDevices();

    // Filter video devices (cameras)
    const videoDevices = devices.filter(device => device.kind === 'videoinput');
    console.log('Video devices found:', videoDevices);

    if (videoDevices.length < 2) {
        console.log('Only one camera found, unable to switch.');
        return;
    }

    // Find the front and back cameras by checking the label
    const frontCamera = videoDevices.find(device => device.label.toLowerCase().includes('front'));
    const backCamera = videoDevices.find(device => device.label.toLowerCase().includes('back'));

    // Determine which camera to switch to
    let nextCamera = null;
    if (currentCameraId === backCamera?.deviceId) {
        nextCamera = frontCamera;
    } else if (currentCameraId === frontCamera?.deviceId) {
        nextCamera = backCamera;
    } else {
        // Default to back camera if none selected yet
        nextCamera = backCamera;
    }

    if (nextCamera) {
        // Stop the current camera and close it
        await localTracks[1].stop();
        await localTracks[1].close();

        // Switch to the new camera
        currentCameraId = nextCamera.deviceId;

        // Create a new camera track for the next camera
        localTracks[1] = await AgoraRTC.createCameraTrack({ cameraId: currentCameraId });

        // Play the new camera stream
        localTracks[1].play(`user-${UID}`);

        // Republish the new camera track
        await client.publish([localTracks[1]]);
    } else {
        console.log('No suitable camera found.');
    }
};

// Theme Management
let currentTheme = localStorage.getItem('theme') || 'default';

// Apply saved theme on page load
document.addEventListener('DOMContentLoaded', () => {
    applyTheme(currentTheme);
    updateActiveThemeOption();
});

function applyTheme(theme) {
    const body = document.body;

    // Remove all theme classes
    body.classList.remove('theme-ocean', 'theme-forest', 'theme-sunset', 'theme-light', 'theme-brutalist');

    // Apply new theme class (except for default)
    if (theme !== 'default') {
        body.classList.add(`theme-${theme}`);
    }

    currentTheme = theme;
    localStorage.setItem('theme', theme);
}

function updateActiveThemeOption() {
    const themeOptions = document.querySelectorAll('.theme-option');
    themeOptions.forEach(option => {
        option.classList.remove('active');
        if (option.dataset.theme === currentTheme) {
            option.classList.add('active');
        }
    });
}

function toggleThemeMenu() {
    const themeMenu = document.getElementById('theme-menu');
    themeMenu.classList.toggle('show');
}

// Close theme menu when clicking outside
document.addEventListener('click', (e) => {
    const themeToggle = document.getElementById('theme-toggle');
    const themeMenu = document.getElementById('theme-menu');

    if (!themeToggle.contains(e.target) && !themeMenu.contains(e.target)) {
        themeMenu.classList.remove('show');
    }
});

// Event Listeners
document.getElementById('join-btn').addEventListener('click', joinStream);
document.getElementById('leave-btn').addEventListener('click', leaveAndRemoveLocalStream);
document.getElementById('mic-btn').addEventListener('click', toggleMic);
document.getElementById('camera-btn').addEventListener('click', toggleCamera);
document.getElementById('switch-camera-btn').addEventListener('click', switchCamera);

// Theme toggle event listener
document.getElementById('theme-toggle').addEventListener('click', toggleThemeMenu);

// Theme option event listeners
document.querySelectorAll('.theme-option').forEach(option => {
    option.addEventListener('click', () => {
        const theme = option.dataset.theme;
        applyTheme(theme);
        updateActiveThemeOption();
        document.getElementById('theme-menu').classList.remove('show');
    });
});
