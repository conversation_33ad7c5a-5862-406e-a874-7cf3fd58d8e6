
/* CSS Variables for Theme System */
:root {
    /* Default Dark Purple Theme */
    --primary-bg-1: #12091c;
    --primary-bg-2: #080324d1;
    --primary-bg-3: #110236;
    --secondary-bg-1: #12051d;
    --secondary-bg-2: #100522;
    --secondary-bg-3: #170425;
    --text-color: #ffffff;
    --button-bg: rgba(0, 0, 0, 0.6);
    --button-bg-hover: rgba(255, 255, 255, 0.1);
    --button-border: rgba(255, 255, 255, 0.1);
    --button-border-hover: rgba(255, 255, 255, 0.5);
    --control-bg: rgba(76, 175, 80, 0.3);
    --control-border: rgba(76, 175, 80, 0.5);
    --video-border: rgb(255, 255, 255);
    --back-button-bg: wheat;
    --back-button-text: #0d0909;
}

/* Ocean Blue Theme */
.theme-ocean {
    --primary-bg-1: #0f3460;
    --primary-bg-2: #16537e;
    --primary-bg-3: #1e6091;
    --secondary-bg-1: #0a2647;
    --secondary-bg-2: #144272;
    --secondary-bg-3: #205295;
    --text-color: #ffffff;
    --button-bg: rgba(15, 52, 96, 0.6);
    --button-bg-hover: rgba(255, 255, 255, 0.1);
    --button-border: rgba(255, 255, 255, 0.1);
    --button-border-hover: rgba(255, 255, 255, 0.5);
    --control-bg: rgba(30, 96, 145, 0.3);
    --control-border: rgba(30, 96, 145, 0.5);
    --video-border: rgb(255, 255, 255);
    --back-button-bg: #87ceeb;
    --back-button-text: #0a2647;
}

/* Forest Green Theme */
.theme-forest {
    --primary-bg-1: #1b4332;
    --primary-bg-2: #2d5016;
    --primary-bg-3: #40531b;
    --secondary-bg-1: #081c15;
    --secondary-bg-2: #1b4332;
    --secondary-bg-3: #2d5016;
    --text-color: #ffffff;
    --button-bg: rgba(27, 67, 50, 0.6);
    --button-bg-hover: rgba(255, 255, 255, 0.1);
    --button-border: rgba(255, 255, 255, 0.1);
    --button-border-hover: rgba(255, 255, 255, 0.5);
    --control-bg: rgba(64, 83, 27, 0.3);
    --control-border: rgba(64, 83, 27, 0.5);
    --video-border: rgb(255, 255, 255);
    --back-button-bg: #90ee90;
    --back-button-text: #081c15;
}

/* Sunset Orange Theme */
.theme-sunset {
    --primary-bg-1: #8b2635;
    --primary-bg-2: #a0522d;
    --primary-bg-3: #cd853f;
    --secondary-bg-1: #722f37;
    --secondary-bg-2: #8b4513;
    --secondary-bg-3: #a0522d;
    --text-color: #ffffff;
    --button-bg: rgba(139, 38, 53, 0.6);
    --button-bg-hover: rgba(255, 255, 255, 0.1);
    --button-border: rgba(255, 255, 255, 0.1);
    --button-border-hover: rgba(255, 255, 255, 0.5);
    --control-bg: rgba(205, 133, 63, 0.3);
    --control-border: rgba(205, 133, 63, 0.5);
    --video-border: rgb(255, 255, 255);
    --back-button-bg: #ffa500;
    --back-button-text: #722f37;
}

/* Light Theme */
.theme-light {
    --primary-bg-1: #f0f8ff;
    --primary-bg-2: #e6f3ff;
    --primary-bg-3: #ddeeff;
    --secondary-bg-1: #ffffff;
    --secondary-bg-2: #f5f5f5;
    --secondary-bg-3: #e0e0e0;
    --text-color: #333333;
    --button-bg: rgba(255, 255, 255, 0.8);
    --button-bg-hover: rgba(0, 0, 0, 0.1);
    --button-border: rgba(0, 0, 0, 0.2);
    --button-border-hover: rgba(0, 0, 0, 0.4);
    --control-bg: rgba(76, 175, 80, 0.2);
    --control-border: rgba(76, 175, 80, 0.4);
    --video-border: #cccccc;
    --back-button-bg: #4a90e2;
    --back-button-text: #ffffff;
}

/* Neo Brutalist Theme */
.theme-brutalist {
    --primary-bg-1: #000000;
    --primary-bg-2: #ffffff;
    --primary-bg-3: #ffff00;
    --secondary-bg-1: #000000;
    --secondary-bg-2: #ffffff;
    --secondary-bg-3: #ffff00;
    --text-color: #000000;
    --button-bg: #ffffff;
    --button-bg-hover: #ffff00;
    --button-border: #000000;
    --button-border-hover: #000000;
    --control-bg: #ffffff;
    --control-border: #000000;
    --video-border: #000000;
    --back-button-bg: #ffff00;
    --back-button-text: #000000;
}

body {
    position: relative;
    margin: 0;
    padding: 0;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: Arial, sans-serif;
    overflow: hidden;
    background: linear-gradient(45deg, var(--primary-bg-1), var(--primary-bg-2), var(--primary-bg-3));
    background-size: 400% 400%;
    animation: gradientAnimation 15s ease infinite;
    color: var(--text-color);
    transition: all 0.5s ease;
}

@keyframes gradientAnimation {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Gradient overlay animation */
body::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    background-size: 200% 200%;
    animation: gradient-shift 5s ease infinite;
    z-index: -1; /* Layer above the blurred image but below the content */
}


/* Modern gradient background */
body::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--secondary-bg-1), var(--secondary-bg-2), var(--secondary-bg-3));
    background-size: 400% 400%;
    animation: gradientAnimation 15s ease infinite;
    z-index: -2;
}

@keyframes gradientAnimation {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Gradient overlay animation */
body::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    background-size: 200% 200%;
    animation: gradient-shift 5s ease infinite;
    z-index: -1; /* Layer above the blurred image but below the content */
}

@keyframes pulse {
    0% {
        transform: translate(-50%, -50%) scale(1);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.05);
        box-shadow: 0 0 15px rgba(255, 255, 255, 0.4);
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
    }
}

#join-btn {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    background: linear-gradient(135deg, var(--button-bg), rgba(0, 0, 0, 0.4));
    color: var(--text-color);
    font-size: 18px;
    font-weight: bold;
    padding: 15px 30px;
    border: 2px solid var(--button-border);
    border-radius: 30px;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    outline: none;
    touch-action: manipulation;
    z-index: 1000;
    backdrop-filter: blur(8px);
    animation: pulse 2s infinite;
}

#join-btn i {
    font-size: 20px;
    margin-right: 8px;
    transition: transform 0.3s ease;
}

/* Hover effect */
#join-btn:hover {
    background: linear-gradient(135deg, var(--button-bg-hover), rgba(255, 255, 255, 0.05));
    border: 2px solid var(--button-border-hover);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.4);
    transform: translate(-50%, -50%) scale(1.05);
}

#join-btn:hover i {
    transform: rotate(15deg);
}

/* Touch and click effect */
#join-btn:active {
    background-color: #e8ece8; /* Even darker background on click */
    box-shadow: 0 3px 4px rgba(0, 0, 0, 0.3); /* Reduced shadow on press */
    transform: translateY(2px); /* Slightly press down */
    border: 2px solid #ffffff; /* Keep light yellow border on press */
}

  
  /* Media query for screens wider than 456px (mobile view) */
  @media (min-width: 456px) {
    #join-btn {
      font-size: 16px; /* Slightly smaller text */
      padding: 10px 20px; /* Adjust padding */
      width: auto; /* Ensure button is adaptive */
      max-width: 300px; /* Limit max width for better visuals */
      border-radius: 12px; /* More rounded corners for mobile */
    }
  }
  
  /* General responsiveness for screens above mobile */
  @media (min-width: 769px) {
    #join-btn {
      font-size: 20px; /* Larger text for bigger screens */
      padding: 14px 28px; /* More padding for emphasis */
    }
  }
  


/* Video Streams Layout */
#video-streams {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    padding: 20px;
    width: 90%;
    height: 90vh;
    overflow-y: auto;
    max-width: 1920px;
    margin: 0 auto;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.5) transparent;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
    touch-action: pan-y pinch-zoom;
}

#video-streams::-webkit-scrollbar {
    width: 6px;
}

#video-streams::-webkit-scrollbar-track {
    background: transparent;
}

#video-streams::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.5);
    border-radius: 3px;
}

/* Square-Shaped Video Container */
.video-container {
    position: relative;
    width: 100%;
    padding-top: 100%; /* 1:1 aspect ratio for square shape */
    background-color: #fdfffd;
    border: 2px solid var(--video-border);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    will-change: transform;
    backface-visibility: hidden;
}

.video-container:hover {
    transform: scale(1.02);
}

.video-player {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Stream Controls */
#stream-controls {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 1.5rem;
    padding: 1.25rem;
    border-radius: 35px;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

#stream-controls button {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--control-bg);
    border: 2px solid var(--control-border);
    cursor: pointer;
    margin: 0;
    transition: all 0.3s ease;
    color: var(--text-color);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    font-size: 0;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}

#stream-controls button i {
    font-size: 24px;
    transition: all 0.3s ease;
    opacity: 0.9;
}

#stream-controls button:hover i {
    transform: scale(1.2);
}

#stream-controls button.active {
    background: rgba(244, 67, 54, 0.3);
    border-color: rgba(244, 67, 54, 0.5);
}

#stream-controls button.active i {
    color: #f44336;
    opacity: 1;
}
button:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    background-size: 200% 200%;
    animation: gradient-shift 5s ease infinite;
    z-index: -1; /* Layer above the blurred image but below the content */
}
#stream-controls button:hover, #stream-controls button:active {
    background: rgba(76, 175, 80, 0.5);
    border-color: rgba(76, 175, 80, 0.8);
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(76, 175, 80, 0.3);
}

#stream-controls button i {
    font-size: 24px;
    pointer-events: none;
}

#stream-controls button span {
    display: none;
}

#stream-controls button:hover i {
    opacity: 1;
}

#stream-controls button:not(.active):hover {
    background: rgba(76, 175, 80, 0.4);
    border-color: rgba(76, 175, 80, 0.8);
}

#stream-controls button.active:hover {
    background: rgba(244, 67, 54, 0.4);
    border-color: rgba(244, 67, 54, 0.8);
}

button:active {
    background: rgba(255, 255, 255, 0.4);
    transform: scale(0.95);
    border-color: rgba(255, 255, 255, 1);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.6);
}

/* Responsive Layout for Different Screen Sizes */
@media screen and (max-width: 768px) {
    body {
        padding: 8px;
    }

    #video-streams {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 8px;
        height: 85vh;
        padding: 12px;
        touch-action: pan-y pinch-zoom;
    }

    .video-container {
        padding-top: 100%; /* maintain square shape */
        border-width: 1px;
    }
}

@media screen and (orientation: landscape) and (max-height: 500px) {
    #video-streams {
        height: 65vh;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    #stream-controls {
        flex-direction: row;
        padding: 8px;
        gap: 8px;
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        width: auto;
        justify-content: center;
    }

    button {
        width: auto;
        padding: 8px 15px;
    }

    .video-container {
        padding-top: 56.25%; /* 16:9 aspect ratio */
    }
    #video-streams {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        height: 65vh;
        gap: 10px;
        padding: 10px;
    }

    #stream-controls {
        flex-direction: column;
        align-items: center;
        position: fixed;
        bottom: 20px;
        left: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.6);
        padding: 10px;
        backdrop-filter: blur(5px);
    }

    button {
        width: 90%;
        font-size: 14px;
        padding: 12px;
        margin: 5px 0;
        border-radius: 25px;
        background: rgba(255, 255, 255, 0.1);
    }

    #join-btn {
        width: 85%;
        padding: 15px;
        font-size: 16px;
        margin: 10px auto;
        display: block;
    }
}

/* For Tablets and Smaller Laptops */
@media screen and (min-width: 769px) and (max-width: 1024px) {
    #video-streams {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    button {
        font-size: 14px;
    }

    #join-btn {
        font-size: 16px;
        padding: 15px 30px;
    }
}

/* For Large Screens (Laptops and Desktops) */
@media screen and (min-width: 1025px) {
    #video-streams {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    button {
        font-size: 16px;
    }

    #join-btn {
        font-size: 18px;
        padding: 20px 40px;
    }
}
@media (max-width: 768px) {
    .event {
        flex: 1 1 100%; /* Full width for smaller screens like tablets or mobiles */
    }

    .theme-menu {
        right: 10px;
        top: 70px;
        min-width: 130px;
        padding: 12px;
    }

    .theme-toggle {
        right: 10px;
        top: 15px;
        width: 45px;
        height: 45px;
        font-size: 18px;
    }

    .back-button {
        left: 10px;
        top: 15px;
        width: 45px;
        height: 45px;
        font-size: 18px;
    }
}

@media (max-width: 480px) {
    #events {
        padding: 10px;
    }

    .event h3 {
        font-size: 16px;
    }
}

.back-button {
    position: absolute;
    top: 20px;
    left: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    font-size: 20px;
    color: var(--back-button-text);
    background-color: var(--back-button-bg);
    border: none;
    border-radius: 50%;
    text-decoration: none;
    cursor: pointer;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.3s ease;
}

.back-button:hover {
    background-color: rgb(255, 255, 255); /* Yellow on hover */
    transform: scale(1.1); /* Slightly enlarge the button on hover */
    box-shadow: 0 0 15px 5px rgb(255, 255, 255); /* Glow effect */
}

.back-button:active {
    background-color: #ffffff; /* Slightly darker yellow when clicked (touch effect) */
    transform: scale(1); /* Normal size when pressed */
    box-shadow: 0 0 10px 4px #ffffff; /* Glow effect when clicked */
}

.back-button i {
    margin: 0;
}

/* Theme Toggle Button */
.theme-toggle {
    position: absolute;
    top: 20px;
    right: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    font-size: 20px;
    color: var(--back-button-text);
    background-color: var(--back-button-bg);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.3s ease;
    z-index: 1001;
}

.theme-toggle:hover {
    background-color: rgb(255, 255, 255);
    transform: scale(1.1);
    box-shadow: 0 0 15px 5px rgb(255, 255, 255);
}

.theme-toggle:active {
    background-color: #ffffff;
    transform: scale(1);
    box-shadow: 0 0 10px 4px #ffffff;
}

.theme-toggle i {
    margin: 0;
}

/* Theme Menu */
.theme-menu {
    position: absolute;
    top: 80px;
    right: 20px;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 15px;
    display: none;
    flex-direction: column;
    gap: 10px;
    z-index: 1002;
    min-width: 150px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-menu.show {
    display: flex;
}

.theme-option {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-color);
}

.theme-option:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.theme-option.active {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.theme-preview {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.theme-preview-default {
    background: linear-gradient(45deg, #12091c, #080324d1, #110236);
}

.theme-preview-ocean {
    background: linear-gradient(45deg, #0f3460, #16537e, #1e6091);
}

.theme-preview-forest {
    background: linear-gradient(45deg, #1b4332, #2d5016, #40531b);
}

.theme-preview-sunset {
    background: linear-gradient(45deg, #8b2635, #a0522d, #cd853f);
}

.theme-preview-light {
    background: linear-gradient(45deg, #f0f8ff, #e6f3ff, #ddeeff);
}

.theme-preview-brutalist {
    background: linear-gradient(45deg, #000000, #ffffff, #ffff00);
}

/* Neo Brutalist Specific Overrides */
.theme-brutalist body {
    background: #ffffff !important;
    background-image: none !important;
    animation: none !important;
    font-family: 'Courier New', monospace !important;
}

.theme-brutalist body::before,
.theme-brutalist body::after {
    display: none !important;
}

.theme-brutalist #join-btn {
    background: #ffff00 !important;
    color: #000000 !important;
    border: 4px solid #000000 !important;
    border-radius: 0 !important;
    box-shadow: 8px 8px 0 #000000 !important;
    animation: none !important;
    font-family: 'Courier New', monospace !important;
    font-weight: 900 !important;
    text-transform: uppercase !important;
    letter-spacing: 2px !important;
}

.theme-brutalist #join-btn:hover {
    background: #000000 !important;
    color: #ffff00 !important;
    border: 4px solid #ffff00 !important;
    box-shadow: 8px 8px 0 #ffff00 !important;
    transform: translate(-50%, -50%) translate(-4px, -4px) !important;
}

.theme-brutalist #video-streams {
    background: #ffffff !important;
    border: 4px solid #000000 !important;
    border-radius: 0 !important;
    backdrop-filter: none !important;
    box-shadow: 8px 8px 0 #000000 !important;
}

.theme-brutalist .video-container {
    background: #ffff00 !important;
    border: 4px solid #000000 !important;
    border-radius: 0 !important;
    box-shadow: 4px 4px 0 #000000 !important;
    transition: none !important;
}

.theme-brutalist .video-container:hover {
    transform: translate(-2px, -2px) !important;
    box-shadow: 6px 6px 0 #000000 !important;
}

.theme-brutalist #stream-controls {
    background: #000000 !important;
    border: 4px solid #ffff00 !important;
    border-radius: 0 !important;
    backdrop-filter: none !important;
    box-shadow: 8px 8px 0 #ffff00 !important;
}

.theme-brutalist #stream-controls button {
    background: #ffffff !important;
    border: 3px solid #000000 !important;
    border-radius: 0 !important;
    color: #000000 !important;
    box-shadow: 3px 3px 0 #000000 !important;
    transition: none !important;
}

.theme-brutalist #stream-controls button:hover {
    background: #ffff00 !important;
    transform: translate(-2px, -2px) scale(1) !important;
    box-shadow: 5px 5px 0 #000000 !important;
}

.theme-brutalist #stream-controls button.active {
    background: #ff0000 !important;
    color: #ffffff !important;
    border: 3px solid #000000 !important;
}

.theme-brutalist .back-button,
.theme-brutalist .theme-toggle {
    background: #ffff00 !important;
    color: #000000 !important;
    border: 3px solid #000000 !important;
    border-radius: 0 !important;
    box-shadow: 4px 4px 0 #000000 !important;
    transition: none !important;
}

.theme-brutalist .back-button:hover,
.theme-brutalist .theme-toggle:hover {
    background: #000000 !important;
    color: #ffff00 !important;
    border: 3px solid #ffff00 !important;
    box-shadow: 4px 4px 0 #ffff00 !important;
    transform: translate(-2px, -2px) scale(1) !important;
}

.theme-brutalist .theme-menu {
    background: #ffffff !important;
    border: 4px solid #000000 !important;
    border-radius: 0 !important;
    backdrop-filter: none !important;
    box-shadow: 8px 8px 0 #000000 !important;
}

.theme-brutalist .theme-option {
    color: #000000 !important;
    border-radius: 0 !important;
    font-family: 'Courier New', monospace !important;
    font-weight: bold !important;
    text-transform: uppercase !important;
}

.theme-brutalist .theme-option:hover {
    background: #ffff00 !important;
    transform: none !important;
    border: 2px solid #000000 !important;
}

.theme-brutalist .theme-option.active {
    background: #000000 !important;
    color: #ffff00 !important;
    border: 2px solid #ffff00 !important;
}
